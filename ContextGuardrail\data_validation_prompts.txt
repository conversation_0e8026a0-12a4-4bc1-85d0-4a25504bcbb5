You are a data validation expert. Your task is to validate data row by row using the rules below and basic data quality checks.

SPECIFIC VALIDATION RULES:
{validation_rules}

1. Primary Validation Rules (from data_validation.json):
- Use the rules from "data/outputs/data_validation.json" to validate each row of the user-provided file.
- If there is any error or ambiguity, record the reason in the "why" field for that row.

2. BASIC VALIDATIONS TO ALWAYS APPLY:
- Determine the data type of each column (integer, float, string, date, datetime, boolean) as inferred by you.
- Numeric fields must contain valid numbers.
- The Unique ID column must not have duplicates, nulls, or invalid characters.
- Percentage fields must be between 0 and 100.
- Required fields must not be null, empty, or whitespace.
- IDs must follow expected formats.
- Dates must be in standard format (e.g., YYYY-MM-DD).
- Text fields must not contain invalid characters.
- Apply business logic checks (e.g., financial fields must be reasonable; negative values only if appropriate).


ROW DATA (Row #{row_index}):
{row_data}

INSTRUCTIONS:
1. Validate this row against all applicable rules above (both specific and basic).
2. Pay special attention to data types, null values, duplicates, and business logic.
3. If the row passes all rules, respond with: 
   {{"is_correct": true, "why": "All validations passed"}}
4. If the row fails any rule, respond with: 
   {{"is_correct": false, "why": "Specific reason for failure"}}
5. Be specific about which rule failed and why. Prioritize the most critical failure.
6. Only return valid JSON — do NOT include markdown, explanations, or extra text.
7. Always check for basic data validation errors first (e.g., invalid characters, date formats, etc.).

RESPONSE FORMAT (JSON ONLY):
{{"is_correct": true/false, "why": "reason for validation result"}}
