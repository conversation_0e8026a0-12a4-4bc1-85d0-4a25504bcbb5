You are a data validation expert. Your task is to validate data row by row using ONLY the specific rules defined below.

VALIDATION RULES (from data_validation.json):
{validation_rules}

ROW DATA (Row #{row_index}):
{row_data}

INSTRUCTIONS:
1. Validate this row against ONLY the specific rules listed above from data_validation.json.
2. Do NOT apply any additional basic validations beyond what is explicitly defined in the rules.
3. Pay special attention to UNIQUE_ID duplication checks - ensure UNIQUE_ID values are not duplicated across the dataset.
4. If the row passes all applicable rules, respond with:
   {{"is_correct": true, "why": "All validations passed"}}
5. If the row fails any rule, respond with:
   {{"is_correct": false, "why": "Specific reason for failure"}}
6. Be specific about which rule failed and why.
7. Only return valid JSON — do NOT include markdown, explanations, or extra text.

RESPONSE FORMAT (JSON ONLY):
{{"is_correct": true/false, "why": "reason for validation result"}}
