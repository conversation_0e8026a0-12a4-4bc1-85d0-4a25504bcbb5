import os
import json
import yaml
import pandas as pd
from pathlib import Path
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables (for OpenAI API key)
load_dotenv()

def load_model_config(config_path="config/model_config.yaml"):
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    return config["openai"]["model"], config["openai"]["temperature"]


def load_validation_rules(rules_path):
    with open(rules_path, "r", encoding="utf-8") as f:
        return json.load(f)

def log_error(log_path, message):
    log_path.parent.mkdir(parents=True, exist_ok=True)
    with open(log_path, "a", encoding="utf-8") as f:
        f.write(message + "\n")

def create_row_prompt(validation_rules, row_index, row_data):
    # Format rules as text for the prompt - ONLY use rules from data_validation.json
    rules_text = ""
    for i, rule in enumerate(validation_rules, 1):
        rule_desc = rule.get("rule", "")
        rule_type = rule.get("type", "")
        columns = rule.get("columns", [])
        rules_text += f"{i}. {rule_desc}\n"
        if columns:
            rules_text += f"   - Applies to columns: {', '.join(columns)}\n"
        rules_text += f"   - Type: {rule_type}\n\n"

    # Format row data
    row_text = ""
    for col, val in row_data.items():
        row_text += f"- {col}: {val}\n"

    # Create a clean prompt that only uses the JSON rules and removes hardcoded basic validations
    clean_prompt = f"""You are a data validation expert. Your task is to validate data row by row using ONLY the specific rules defined below.

VALIDATION RULES (from data_validation.json):
{rules_text}

ROW DATA (Row #{row_index + 1}):
{row_text}

INSTRUCTIONS:
1. Validate this row against ONLY the specific rules listed above from data_validation.json.
2. Do NOT apply any additional basic validations beyond what is explicitly defined in the rules.
3. If the row passes all applicable rules, respond with:
   {{"is_correct": true, "why": "All validations passed"}}
4. If the row fails any rule, respond with:
   {{"is_correct": false, "why": "Specific reason for failure"}}
5. Be specific about which rule failed and why.
6. Only return valid JSON — do NOT include markdown, explanations, or extra text.

RESPONSE FORMAT (JSON ONLY):
{{"is_correct": true/false, "why": "reason for validation result"}}"""

    return clean_prompt

def main():
    # Paths
    config_path = "config/model_config.yaml"
    rules_path = "data/outputs/data_validation.json"
    log_path = Path("data/audit_logs/data_validation.log")

    # Load config and rules (no longer loading prompt template as we generate it dynamically)
    model, temperature = load_model_config(config_path)
    validation_rules = load_validation_rules(rules_path)

    # OpenAI client
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Get file path from user
    file_path = input("\U0001F4E5 Enter path to the Excel file to validate: ").strip()
    if not os.path.exists(file_path):
        print(f"\u274C File not found: {file_path}")
        return

    # Read Excel file
    df = pd.read_excel(file_path)
    results = []
    print(f"\u2705 Loaded {len(df)} rows.")

    # Validate each row
    for idx, row in df.iterrows():
        row_data = row.to_dict()
        prompt = create_row_prompt(validation_rules, idx, row_data)
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=500,
                top_k=10
            )
            content = response.choices[0].message.content
            result = json.loads(content)
            is_correct = result.get("is_correct", False)
            why = result.get("why", "")
        except Exception as e:
            is_correct = False
            why = f"Validation error: {e}"
            log_error(log_path, f"Row {idx+1}: {why}")
        results.append({"is_correct": is_correct, "why": why})

    # Add results to DataFrame and save
    df["is_correct"] = [r["is_correct"] for r in results]
    df["why"] = [r["why"] for r in results]
    output_path = os.path.splitext(file_path)[0] + "_validated.xlsx"
    df.to_excel(output_path, index=False)
    print(f"\U0001F4BE Validation complete. Results saved to: {output_path}")

if __name__ == "__main__":
    main()
