import os
import json
import yaml
import pandas as pd
from pathlib import Path
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables (for OpenAI API key)
load_dotenv()

def load_model_config(config_path="config/model_config.yaml"):
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    return config["openai"]["model"], config["openai"]["temperature"]

def load_prompt(prompt_path):
    with open(prompt_path, "r", encoding="utf-8") as f:
        return f.read()

def load_validation_rules(rules_path):
    with open(rules_path, "r", encoding="utf-8") as f:
        return json.load(f)

def log_error(log_path, message):
    log_path.parent.mkdir(parents=True, exist_ok=True)
    with open(log_path, "a", encoding="utf-8") as f:
        f.write(message + "\n")

def create_row_prompt(prompt_template, validation_rules, row_index, row_data):
    # Format rules as text for the prompt
    rules_text = ""
    for i, rule in enumerate(validation_rules, 1):
        rule_desc = rule.get("rule", "")
        rule_type = rule.get("type", "")
        columns = rule.get("columns", [])
        rules_text += f"{i}. {rule_desc}\n"
        if columns:
            rules_text += f"   - Applies to columns: {', '.join(columns)}\n"
        rules_text += f"   - Type: {rule_type}\n\n"
    # Format row data
    row_text = ""
    for col, val in row_data.items():
        row_text += f"- {col}: {val}\n"
    # Fill in the template
    return prompt_template.format(
        validation_rules=rules_text,
        row_index=row_index + 1,
        row_data=row_text
    )

def main():
    # Paths
    config_path = "config/model_config.yaml"
    prompt_path = "ContextGuardrail/data_validation_prompts.txt"
    rules_path = "data/outputs/data_validation.json"
    log_path = Path("data/audit_logs/data_validation.log")

    # Load config, prompt, and rules
    model, temperature = load_model_config(config_path)
    prompt_template = load_prompt(prompt_path)
    validation_rules = load_validation_rules(rules_path)

    # OpenAI client
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Get file path from user
    file_path = input("\U0001F4E5 Enter path to the Excel file to validate: ").strip()
    if not os.path.exists(file_path):
        print(f"\u274C File not found: {file_path}")
        return

    # Read Excel file
    df = pd.read_excel(file_path)
    results = []
    print(f"\u2705 Loaded {len(df)} rows.")

    # Validate each row
    for idx, row in df.iterrows():
        row_data = row.to_dict()
        prompt = create_row_prompt(prompt_template, validation_rules, idx, row_data)
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=256
            )
            content = response.choices[0].message.content
            result = json.loads(content)
            is_correct = result.get("is_correct", False)
            why = result.get("why", "")
        except Exception as e:
            is_correct = False
            why = f"Validation error: {e}"
            log_error(log_path, f"Row {idx+1}: {why}")
        results.append({"is_correct": is_correct, "why": why})

    # Add results to DataFrame and save
    df["is_correct"] = [r["is_correct"] for r in results]
    df["why"] = [r["why"] for r in results]
    output_path = os.path.splitext(file_path)[0] + "_validated.xlsx"
    df.to_excel(output_path, index=False)
    print(f"\U0001F4BE Validation complete. Results saved to: {output_path}")

if __name__ == "__main__":
    main()
