import logging
import os

def setup_validation_logger(log_path: str):
    os.makedirs(os.path.dirname(log_path), exist_ok=True)
    logger = logging.getLogger("data_validation_agent")
    logger.setLevel(logging.DEBUG)
    # Prevent duplicate handlers if re-imported
    if not logger.handlers:
        # File handler for INFO and above
        fh = logging.FileHandler(log_path, encoding='utf-8')
        fh.setLevel(logging.INFO)
        fh_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        fh.setFormatter(fh_formatter)
        logger.addHandler(fh)
        # Console handler for DEBUG
        ch = logging.StreamHandler()
        ch.setLevel(logging.DEBUG)
        ch_formatter = logging.Formatter('%(levelname)s - %(message)s')
        ch.setFormatter(ch_formatter)
        logger.addHandler(ch)
    return logger
