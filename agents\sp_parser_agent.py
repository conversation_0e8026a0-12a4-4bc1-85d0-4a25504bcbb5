import os
import json
import yaml
from openai import OpenAI
from dotenv import load_dotenv

# Load OpenAI key and config
load_dotenv()
with open("config/model_config.yaml", "r") as f:
    config = yaml.safe_load(f)

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
model_name = config.get("llm", {}).get("model", "gpt-4")

def load_prompt_template():
    with open("ContextGuardrail/sp_parser_prompts.txt", "r", encoding="utf-8") as f:
        return f.read()

def extract_rules(procedure: dict, template: list, prompt_template: str) -> dict:
    required_columns = [col["FILE_COLUMN_NAME"].strip() for col in template]
    data_types = {col["FILE_COLUMN_NAME"].strip(): col["Data_Type"] for col in template}

    prompt = prompt_template.format(
        required_columns=json.dumps(required_columns, indent=2),
        data_types=json.dumps(data_types, indent=2),
        procedure_text=procedure["procedure_definition"],
        procedure_name=procedure["procedure_name"]
    )

    try:
        response = client.chat.completions.create(
            model=model_name,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2
        )
        return json.loads(response.choices[0].message.content)
    except Exception as e:
        print(f"❌ Error for {procedure['procedure_name']}: {e}")
        return {"schema_validation": [], "data_validation": []}

def main():
    with open("data/inputs/stored_procedures.json", "r") as f:
        procedures = json.load(f)

    with open("data/inputs/FundHolding_Metadata_Columns 1.json", "r") as f:
        column_template = json.load(f)

    prompt_template = load_prompt_template()
    schema_rules, data_rules = [], []

    for proc in procedures:
        print(f"⏳ Processing {proc['procedure_name']}...")
        result = extract_rules(proc, column_template, prompt_template)
        schema_rules.extend(result.get("schema_validation", []))
        data_rules.extend(result.get("data_validation", []))

    os.makedirs("outputs", exist_ok=True)
    with open("outputs/schema_validation.json", "w") as f:
        json.dump(schema_rules, f, indent=2)
    with open("outputs/data_validation.json", "w") as f:
        json.dump(data_rules, f, indent=2)

    print("✅ Completed: schema_validation.json and data_validation.json")

if __name__ == "__main__":
    main()
